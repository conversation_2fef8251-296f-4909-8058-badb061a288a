import React, { useEffect, useState } from 'react';
import Dialog from '@mui/material/Dialog';
import DialogTitle from '@mui/material/DialogTitle';
import DialogContent from '@mui/material/DialogContent';
import { useTheme } from '@mui/material/styles';
import FormBuilder from '../../components/form/FormBuilder';
import { getPersonas } from '../../services/personaService';
import { useProject } from '../../contexts/ProjectContext';

const PromptType = {
  CONVERSATIONAL: 'Conversational',
  SUMMARY: 'Summary',
  FALLBACK: 'Fallback',
};

const PromptStatus = {
  DRAFT: 'Draft',
  ACTIVE: 'Active',
  ARCHIVED: 'Archived',
  TESTING: 'Testing',
  INACTIVE: 'Inactive',
};

const PromptModel = {
  GPT_4: 'gpt-4',
  GPT_3_5: 'gpt-3.5-turbo',
  CLAUDE_2: 'claude-2',
  LLAMA_2: 'llama-2',
  GEMINI_PRO: 'gemini-pro',
};

const getFields = (personas = []) => [
  {
    name: 'type',
    label: 'Type',
    type: 'select',
    required: true,
    options: Object.entries(PromptType).map(([key, value]) => ({ label: value, value })),
    gridProps: { xs: 12 },
    size: 'large',
    helperText: 'Select the type of prompt',
  },
  {
    name: 'prompt_text',
    label: 'Prompt Text',
    type: 'textarea',
    required: true,
    gridProps: { xs: 12 },
    size: 'large',
    rows: 6,
    multiline: true,
    minRows: 4,
    maxRows: 8,
    sx: {
      '& .MuiInputBase-root': {
        fontFamily: 'monospace',
        fontSize: '0.875rem',
      },
    },
  },
  {
    name: 'tagging_prompt_text',
    label: 'Tagging Prompt (Optional)',
    type: 'textarea',
    required: false,
    gridProps: { xs: 12 },
    size: 'large',
    rows: 4,
    multiline: true,
    minRows: 3,
    maxRows: 6,
    helperText:
      'This tagging prompt classifies the call based on predefined criteria. Both prompts will be sent together.',
    conditional: (values) => values.type === 'Summary',
    sx: {
      '& .MuiInputBase-root': {
        fontFamily: 'monospace',
        fontSize: '0.875rem',
      },
      '& .MuiFormHelperText-root': {
        color: '#6c757d',
        fontSize: '0.75rem',
        marginTop: '8px',
      },
    },
  },
  {
    name: 'status',
    label: 'Status',
    type: 'select',
    required: true,
    options: Object.entries(PromptStatus).map(([key, value]) => ({ label: value, value })),
    gridProps: { xs: 12 },
    size: 'large',
    disabled: true,
  },
  {
    name: 'model',
    label: 'Model',
    type: 'select',
    required: true,
    options: Object.entries(PromptModel).map(([key, value]) => ({ label: value, value })),
    gridProps: { xs: 12 },
    size: 'large',
    helperText: 'Select the model for this prompt',
  },
  {
    name: 'persona_id',
    label: 'Persona',
    type: 'select',
    required: false,
    options: personas.map((persona) => ({
      label: persona.name || 'Unknown Persona',
      value: persona.id,
    })),
    gridProps: { xs: 12 },
    size: 'large',
    helperText: 'Select a persona to associate with this prompt',
  },
];

const PromptFormModal = ({ open, onClose, onSubmit, initialValues, mode }) => {
  const theme = useTheme();
  const [personas, setPersonas] = useState([]);
  const { selectedProjectId } = useProject();

  useEffect(() => {
    const fetchPersonas = async () => {
      try {
        const response = await getPersonas(selectedProjectId);
        if (response) {
          setPersonas(response.items || []);
        }
      } catch (error) {
        console.error('Error fetching personas:', error);
      }
    };

    if (open) {
      fetchPersonas();
    }
  }, [open, selectedProjectId]);

  const fields = getFields(personas);

  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth='sm'
      fullWidth
      PaperProps={{
        elevation: 1,
        sx: {
          borderRadius: 0,
          overflow: 'hidden',
        },
      }}
    >
      <DialogTitle
        sx={{
          bgcolor: theme.palette.primary.main,
          color: theme.palette.primary.contrastText,
          py: 2,
          fontSize: '1.25rem',
          fontWeight: 500,
        }}
      >
        {mode === 'edit' ? 'Edit Prompt' : 'Add New Prompt'}
      </DialogTitle>
      <DialogContent sx={{ p: 3, pt: 3 }}>
        <FormBuilder
          fields={fields}
          initialValues={initialValues}
          onSubmit={onSubmit}
          formLayout={{
            fieldSpacing: 0.5,
            direction: 'column',
            variant: 'standard',
            padding: 0,
            buttonAlignment: 'space-between',
            submitButtonProps: {
              variant: 'contained',
              color: 'primary',
              size: 'large',
              sx: { px: 4 },
            },
            cancelButtonProps: {
              variant: 'outlined',
              color: 'inherit',
              size: 'large',
              sx: { px: 4 },
            },
          }}
          submitButtonText={mode === 'edit' ? 'Update' : 'Create'}
          cancelButtonText='Cancel'
          onCancel={onClose}
        />
      </DialogContent>
    </Dialog>
  );
};

export default PromptFormModal;
