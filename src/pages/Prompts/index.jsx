import React, { useEffect, useState, useCallback, forwardRef, useImperativeHandle } from 'react';
import DataTable from '../../components/DataTable/DataTable';
import Button from '../../components/Button';
import {
  listPrompts,
  createPrompt,
  updatePrompt,
  deletePrompt,
  getPromptVersions,
  createPromptVersion,
  updatePromptVersion,
} from '../../services/promptService';
import PromptFormModal from './PromptFormModal';
import { useNavigate } from 'react-router-dom';
import { Typography, Link, Box } from '@mui/material';
import TextDisplayDialog from '../../components/TextDisplayDialog/TextDisplayDialog';
import VersionsDialog from '../../components/VersionsDialog/VersionsDialog';
import { useProject } from '../../contexts/ProjectContext';

const getColumnsBase = (handleOpenVersionsDialog, handleOpenPromptTextDialog) => [
  {
    headerName: 'Type',
    field: 'type',
    valueGetter: (params) => params.row.type || '',
    renderCell: ({ row, value }) => (
      <Link
        component='button'
        variant='body2'
        onClick={(e) => {
          e.stopPropagation();
          handleOpenVersionsDialog(row);
          return false;
        }}
      >
        {value}
      </Link>
    ),
  },
  {
    headerName: 'Prompt Text',
    field: 'current_version.prompt_text',
    valueGetter: (params) => params.row.current_version?.prompt_text || '',
    renderCell: ({ value }) => {
      return (
        <Typography
          sx={{
            overflow: 'hidden',
            textOverflow: 'ellipsis',
            display: '-webkit-box',
            WebkitLineClamp: 2,
            WebkitBoxOrient: 'vertical',
            lineHeight: '1.2em',
            maxHeight: '2.4em',
            cursor: 'pointer',
          }}
          onClick={() => handleOpenPromptTextDialog(value)}
        >
          {value}
        </Typography>
      );
    },
  },
  {
    headerName: 'Status',
    field: 'status',
    valueGetter: (params) => params.row.status || '',
  },
  {
    headerName: 'Model',
    field: 'current_version.model',
    valueGetter: (params) => params.row.current_version?.model || '',
  },
  {
    headerName: 'Persona',
    field: 'persona_id',
    valueGetter: (params) => params.row.persona_id || '',
  },
  {
    headerName: 'Version',
    field: 'current_version.version',
    valueGetter: (params) => params.row.current_version?.version || '',
  },
];

const Prompts = forwardRef(
  ({ projectId: propProjectId, inProjectDetail = false, setError }, ref) => {
    const [textDialog, setTextDialog] = useState({
      open: false,
      title: '',
      content: '',
    });

    // navigate is not used in this component but kept for future use
    const navigate = useNavigate();

    const handleOpenTextDialog = (content, title = 'Prompt Text') => {
      setTextDialog({
        open: true,
        title,
        content: content || '',
      });
    };

    const handleCloseTextDialog = () => {
      setTextDialog((prev) => ({
        ...prev,
        open: false,
      }));
    };

    const { selectedProjectId } = useProject();
    const projectId = propProjectId || selectedProjectId;
    const [prompts, setPrompts] = useState([]);
    const [loading, setLoading] = useState(false);
    const [page, setPage] = useState(0);
    const [rowsPerPage, setRowsPerPage] = useState(10);
    const [totalCount, setTotalCount] = useState(0);
    const [personas, setPersonas] = useState([]);
    const [selectedPersonaId, setSelectedPersonaId] = useState('');

    const [modalOpen, setModalOpen] = useState(false);
    const [modalMode, setModalMode] = useState('create');
    const [modalInitialValues, setModalInitialValues] = useState({
      type: 'Conversational',
      prompt_text: '',
      status: 'Draft',
      model: 'gpt-4',
    });
    const [actionLoading, setActionLoading] = useState(false);
    const [actionError, setActionError] = useState(null);

    const [versionsDialogOpen, setVersionsDialogOpen] = useState(false);
    const [selectedPrompt, setSelectedPrompt] = useState(null);
    const [promptVersions, setPromptVersions] = useState([]);

    const fetchPrompts = useCallback(
      async (page = 0, limit = 10) => {
        setLoading(true);
        setError(null);
        try {
          const params = { skip: page * limit, limit };

          if (projectId) {
            params.project_id = projectId;
          }
          if (selectedPersonaId) {
            params.persona_id = selectedPersonaId;
          }

          const res = await listPrompts(params);
          setPrompts(res.items || []);
          setTotalCount(res.metadata?.total ?? res.items?.length ?? 0);
        } catch (err) {
          console.error('Error loading prompts:', err);
          const errorMsg = 'Failed to load prompts';
          setActionError(errorMsg);
          if (setError) setError(errorMsg);
        } finally {
          setLoading(false);
        }
      },
      [projectId, selectedPersonaId, setError]
    );

    useEffect(() => {
      fetchPrompts(page, rowsPerPage);
    }, [fetchPrompts, page, rowsPerPage]);

    const handlePageChange = (event, newPage) => {
      setPage(newPage);
    };

    const handleRowsPerPageChange = (event) => {
      setRowsPerPage(parseInt(event.target.value, 10));
      setPage(0);
    };

    const handleOpenCreate = () => {
      setModalMode('create');
      setModalInitialValues({
        type: 'Conversational',
        prompt_text: '',
        status: 'Active',
        model: 'gpt-4',
      });
      setModalOpen(true);
    };

    const handleOpenEdit = (prompt) => {
      setModalMode('edit');
      setModalInitialValues({
        id: prompt.id,
        persona_id: prompt.persona_id,
        type: prompt.type,
        prompt_text: prompt.current_version?.prompt_text || '',
        status: prompt.status,
        prompt_text: prompt.current_version?.prompt_text || '',
        model: prompt.current_version?.model || 'gpt-4',
        prompt_id: prompt.id,
      });
      setModalMode('edit');
      setModalOpen(true);
    };

    const handleDelete = async (promptId) => {
      if (!window.confirm('Are you sure you want to delete this prompt?')) {
        return;
      }

      setActionLoading(true);
      setActionError(null);
      try {
        await deletePrompt(promptId);
        fetchPrompts(page, rowsPerPage);
      } catch (err) {
        console.error('Error deleting prompt:', err);
        setActionError('Failed to delete prompt');
      } finally {
        setActionLoading(false);
      }
    };

    const handleModalSubmit = async (values) => {
      setActionLoading(true);
      setActionError(null);
      try {
        if (modalMode === 'edit') {
          const changedFields = {};

          changedFields.id = values.id;
          changedFields.prompt_id = values.id;

          Object.keys(values).forEach((key) => {
            if (JSON.stringify(values[key]) !== JSON.stringify(modalInitialValues[key])) {
              changedFields[key] = values[key];
            }
          });

          await updatePrompt(values.id, changedFields);
        } else {
          const promptData = {
            ...values,
            project_id: projectId,
          };
          await createPrompt(promptData);
        }
        setModalOpen(false);
        fetchPrompts(page, rowsPerPage, selectedPersonaId);
      } catch (err) {
        console.error('Error saving prompt:', err);
        setActionError('Failed to save prompt');
      } finally {
        setActionLoading(false);
      }
    };

    const handleOpenVersionsDialog = (prompt) => {
      setSelectedPrompt(prompt);
      setVersionsDialogOpen(true);
      fetchPromptVersions(prompt.id);
    };

    const fetchPromptVersions = async (promptId) => {
      setActionLoading(true);
      try {
        const versions = await getPromptVersions(promptId);
        setPromptVersions(versions || []);
      } catch (err) {
        console.error('Error fetching prompt versions:', err);
        setActionError('Failed to fetch prompt versions');
      } finally {
        setActionLoading(false);
      }
    };

    const handleCreatePromptVersion = async (promptId, versionData) => {
      setActionLoading(true);
      try {
        await createPromptVersion(promptId, versionData);
        await fetchPromptVersions(promptId);
        fetchPrompts(page, rowsPerPage, selectedPersonaId);
        return true;
      } catch (err) {
        console.error('Error creating prompt version:', err);
        setActionError('Failed to create prompt version');
        return false;
      } finally {
        setActionLoading(false);
      }
    };

    const handleUpdatePromptVersion = async (versionId, versionData) => {
      setActionLoading(true);
      try {
        await updatePromptVersion(versionId, versionData);
        if (selectedPrompt) {
          await fetchPromptVersions(selectedPrompt.id);
          fetchPrompts(page, rowsPerPage, selectedPersonaId);
        }
        return true;
      } catch (err) {
        console.error('Error updating prompt version:', err);
        setActionError('Failed to update prompt version');
        return false;
      } finally {
        setActionLoading(false);
      }
    };

    const getColumns = () => {
      const handleOpenPromptTextDialog = (text) => {
        handleOpenTextDialog(text, 'Prompt Text');
      };
      const columnsBase = getColumnsBase(handleOpenVersionsDialog, handleOpenPromptTextDialog);
      return [
        ...columnsBase,
        {
          headerName: 'Actions',
          field: 'actions',
          renderCell: ({ row }) => (
            <div style={{ display: 'flex', gap: 8 }}>
              <Button size='small' variant='outlined' onClick={() => handleOpenVersionsDialog(row)}>
                Versions
              </Button>
            </div>
          ),
        },
      ];
    };

    // Function to render text with click-to-expand functionality
    const renderExpandableText = (text, maxLines = 2) => {
      if (!text) return '-';

      const handleClick = (e) => {
        e.stopPropagation();
        handleOpenTextDialog(text);
      };

      // Calculate if the text is likely to be truncated
      const averageCharsPerLine = 50; // Rough estimate
      const estimatedLines = Math.ceil(text.length / averageCharsPerLine);
      const isTruncated = estimatedLines > maxLines;

      return (
        <Box
          sx={{
            position: 'relative',
            cursor: 'pointer',
          }}
          onClick={handleClick}
        >
          <Typography
            sx={{
              overflow: 'hidden',
              textOverflow: 'ellipsis',
              display: '-webkit-box',
              WebkitLineClamp: maxLines,
              WebkitBoxOrient: 'vertical',
              lineHeight: '1.2em',
              maxHeight: `${maxLines * 1.2}em`,
            }}
          >
            {text}
          </Typography>
          {isTruncated && (
            <Box
              sx={{
                position: 'absolute',
                bottom: 0,
                right: 0,
                fontSize: '0.75rem',
                color: 'primary.main',
                backgroundColor: 'rgba(255, 255, 255, 0.8)',
                padding: '0 4px',
              }}
            >
              ...more
            </Box>
          )}
        </Box>
      );
    };

    const columns = getColumns();

    // Expose methods to parent component
    useImperativeHandle(ref, () => ({
      handleOpenCreate,
    }));

    return (
      <div>
        {/* Text Display Dialog */}
        <TextDisplayDialog
          open={textDialog.open}
          onClose={handleCloseTextDialog}
          title={textDialog.title}
          content={textDialog.content}
          showLineNumbers={false}
        />

        {inProjectDetail && (
          <div
            style={{
              display: 'flex',
              justifyContent: 'flex-end',
              alignItems: 'center',
              marginBottom: 16,
            }}
          >
            <Button
              variant='contained'
              onClick={handleOpenCreate}
              sx={{
                backgroundColor: '#28a745',
                color: 'white',
                borderRadius: '8px',
                textTransform: 'none',
                fontWeight: 600,
                px: 3,
                py: 1,
                '&:hover': {
                  backgroundColor: '#1e7e34',
                },
              }}
            >
              + Add Prompt
            </Button>
          </div>
        )}
        {actionError && <div style={{ color: 'red' }}>{actionError}</div>}
        <DataTable
          data={prompts}
          columns={columns}
          loading={loading || actionLoading}
          totalCount={prompts.length > 0 ? Math.max(totalCount, prompts.length) : totalCount}
          serverSidePagination
          onPageChange={handlePageChange}
          onRowsPerPageChange={(newRowsPerPage) => {
            handleRowsPerPageChange({ target: { value: newRowsPerPage } });
          }}
          emptyStateMessage={loading ? 'Loading...' : 'No prompts found'}
          getRowId={(row) => row.id}
          searchEnabled={false}
        />
        <PromptFormModal
          open={modalOpen}
          onClose={() => setModalOpen(false)}
          onSubmit={handleModalSubmit}
          initialValues={modalInitialValues}
          mode={modalMode}
        />

        {/* Versions Dialog */}
        {selectedPrompt && (
          <VersionsDialog
            open={versionsDialogOpen}
            onClose={() => setVersionsDialogOpen(false)}
            versions={promptVersions}
            entity={selectedPrompt}
            fetchVersions={fetchPromptVersions}
            createVersion={handleCreatePromptVersion}
            updateVersion={handleUpdatePromptVersion}
            fields={[
              {
                name: 'prompt_text',
                label: 'Prompt Text',
                type: 'textarea',
                required: true,
                renderCell: ({ value }) => renderExpandableText(value, 2),
              },
              {
                name: 'model',
                label: 'Model',
                type: 'select',
                required: true,
                options: [
                  { value: 'gpt-4', label: 'GPT-4' },
                  { value: 'gpt-3.5-turbo', label: 'GPT-3.5 Turbo' },
                  { value: 'claude-2', label: 'Claude 2' },
                  { value: 'llama-2', label: 'Llama 2' },
                  { value: 'gemini-pro', label: 'Gemini Pro' },
                ],
              },
              {
                name: 'status',
                label: 'Status',
                type: 'select',
                required: true,
                options: [
                  { value: 'Draft', label: 'Draft' },
                  { value: 'Active', label: 'Active' },
                  { value: 'Inactive', label: 'Inactive' },
                ],
              },
              { name: 'change_notes', label: 'Change Notes', type: 'textarea', required: true },
            ]}
            entityType='prompt'
          />
        )}
      </div>
    );
  }
);

export default Prompts;
