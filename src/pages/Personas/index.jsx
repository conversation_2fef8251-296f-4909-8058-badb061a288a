import React, { useEffect, useState, useCallback, forwardRef, useImperativeHandle } from 'react';
import DataTable from '../../components/DataTable/DataTable';
import Button from '../../components/Button';
import {
  listPersonas,
  createPersona,
  update<PERSON>ersona,
  deletePersona,
  getPersonaVersions,
  createPersonaVersion,
  updatePersonaVersion,
} from '../../services/personaService';
import PersonaFormModal from './PersonaFormModal';
import { useNavigate } from 'react-router-dom';
import {
  Typography,
  Link,
  Box,
  Alert,
  Chip,
  Tooltip,
  IconButton,
  useTheme,
  alpha,
  Paper,
  Stack,
} from '@mui/material';
import VersionsDialog from '../../components/VersionsDialog/VersionsDialog';
import AddIcon from '@mui/icons-material/Add';
import EditIcon from '@mui/icons-material/Edit';
import DeleteIcon from '@mui/icons-material/Delete';
import ArchiveIcon from '@mui/icons-material/Archive';
import HistoryIcon from '@mui/icons-material/History';
import PersonIcon from '@mui/icons-material/Person';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';

const getColumnsBase = (handleOpenVersionsDialog) => [
  {
    headerName: 'Name',
    field: 'name',
    renderCell: ({ row, value }) => (
      <Link
        component='button'
        variant='body2'
        onClick={(e) => {
          e.stopPropagation();
          handleOpenVersionsDialog(row);
          return false;
        }}
      >
        {value}
      </Link>
    ),
  },
  {
    headerName: 'Voice Type',
    field: 'voice_type',
    renderCell: ({ row }) => row.current_version?.voice_type || row.voice_type || '-',
  },
  {
    headerName: 'Engine',
    field: 'engine',
    renderCell: ({ row }) => row.current_version?.engine || row.engine || '-',
  },
  {
    headerName: 'Language',
    field: 'language',
    renderCell: ({ row }) => row.current_version?.language || row.language || '-',
  },
  {
    headerName: 'Phone Number',
    field: 'from_number',
    renderCell: ({ row }) => row.current_version?.from_number || row.from_number || '-',
  },
  {
    headerName: 'Default',
    field: 'is_default',
    renderCell: ({ value }) =>
      value ? (
        <Chip
          icon={<CheckCircleIcon fontSize='small' />}
          label='Yes'
          size='small'
          color='success'
          variant='outlined'
        />
      ) : (
        <Typography variant='body2' color='text.secondary'>
          No
        </Typography>
      ),
  },
];

const Personas = forwardRef(({ projectId, inProjectDetail = false, setError }, ref) => {
  const theme = useTheme();
  const navigate = useNavigate();
  const [personas, setPersonas] = useState([]);
  const [loading, setLoading] = useState(false);
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [totalCount, setTotalCount] = useState(0);

  // Modal state
  const [modalOpen, setModalOpen] = useState(false);
  const [modalMode, setModalMode] = useState('create'); // 'create' or 'edit'
  const [modalInitialValues, setModalInitialValues] = useState({
    name: '',
    voice_type: '',
    engine: '',
    language: '',
    from_number: '',
    is_default: false,
  });
  const [actionLoading, setActionLoading] = useState(false);
  const [actionError, setActionError] = useState(null);

  // Versions dialog state
  const [versionsDialogOpen, setVersionsDialogOpen] = useState(false);
  const [selectedPersona, setSelectedPersona] = useState(null);
  const [personaVersions, setPersonaVersions] = useState([]);

  const fetchPersonas = useCallback(
    async (page = 0, limit = 10) => {
      setLoading(true);
      setError(null);
      try {
        const params = { skip: page * limit, limit };

        if (projectId) {
          params.project_id = projectId;
        }

        const res = await listPersonas(params);
        setPersonas(res.items || []);
        setTotalCount(res.total ?? 0);
      } catch (err) {
        console.error('Error loading personas:', err);
        setError('Failed to load personas');
      } finally {
        setLoading(false);
      }
    },
    [projectId, setError]
  );

  useEffect(() => {
    fetchPersonas(page, rowsPerPage);
  }, [fetchPersonas, page, rowsPerPage]);

  const handlePageChange = (event, newPage) => {
    setPage(newPage);
  };

  const handleRowsPerPageChange = (event) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  // CREATE
  const handleOpenCreate = () => {
    setModalInitialValues({
      name: '',
      voice_type: '',
      engine: '',
      language: '',
      from_number: '',
      is_default: false,
    });
    setModalMode('create');
    setModalOpen(true);
  };

  // EDIT
  const handleOpenEdit = (persona) => {
    setModalInitialValues({
      id: persona.id,
      name: persona.name,
      voice_type: persona.current_version?.voice_type || persona.voice_type || '',
      engine: persona.current_version?.engine || persona.engine || '',
      language: persona.current_version?.language || persona.language || '',
      from_number: persona.current_version?.from_number || persona.from_number || '',
      is_default: persona.is_default,
    });
    setModalMode('edit');
    setModalOpen(true);
  };

  const handleDelete = async (personaId) => {
    setActionLoading(true);
    setActionError(null);
    try {
      await updatePersona(personaId, { status: 'Archived' });
      fetchPersonas(page, rowsPerPage);
    } catch (err) {
      console.error('Error deleting persona:', err);
      setActionError('Failed to delete persona');
    } finally {
      setActionLoading(false);
    }
  };

  const handleModalSubmit = async (values) => {
    setActionLoading(true);
    setActionError(null);
    try {
      if (modalMode === 'create') {
        const payload = { ...values };
        if (projectId) {
          payload.project_id = projectId;
        }
        await createPersona(payload);
      } else {
        await updatePersona(values.id, values);
      }
      setModalOpen(false);
      fetchPersonas(page, rowsPerPage);
    } catch (err) {
      console.error(`Error ${modalMode === 'create' ? 'creating' : 'updating'} persona:`, err);
      setActionError(`Failed to ${modalMode === 'create' ? 'create' : 'update'} persona`);
    } finally {
      setActionLoading(false);
    }
  };

  const handleOpenVersionsDialog = (persona) => {
    setSelectedPersona(persona);
    setVersionsDialogOpen(true);
    fetchPersonaVersions(persona.id);
  };

  const fetchPersonaVersions = async (personaId) => {
    setActionLoading(true);
    try {
      const versions = await getPersonaVersions(personaId);
      setPersonaVersions(versions || []);
    } catch (err) {
      console.error('Error fetching persona versions:', err);
      setActionError('Failed to fetch persona versions');
    } finally {
      setActionLoading(false);
    }
  };

  const handleCreatePersonaVersion = async (personaId, versionData) => {
    setActionLoading(true);
    try {
      await createPersonaVersion(personaId, versionData);
      await fetchPersonaVersions(personaId);
      fetchPersonas(page, rowsPerPage);
      return true;
    } catch (err) {
      console.error('Error creating persona version:', err);
      setActionError('Failed to create persona version');
      return false;
    } finally {
      setActionLoading(false);
    }
  };

  const handleUpdatePersonaVersion = async (versionId, versionData) => {
    setActionLoading(true);
    try {
      await updatePersonaVersion(versionId, versionData);
      if (selectedPersona) {
        await fetchPersonaVersions(selectedPersona.id);
        fetchPersonas(page, rowsPerPage);
      }
      return true;
    } catch (err) {
      console.error('Error updating persona version:', err);
      setActionError('Failed to update persona version');
      return false;
    } finally {
      setActionLoading(false);
    }
  };

  const getColumns = () => {
    const baseColumns = getColumnsBase(handleOpenVersionsDialog);
    return [
      ...baseColumns,
      {
        headerName: 'Actions',
        field: 'actions',
        renderCell: ({ row }) => (
          <Stack direction='row' spacing={1}>
            <Tooltip title='Edit Persona'>
              <IconButton
                size='small'
                onClick={(e) => {
                  e.stopPropagation();
                  handleOpenEdit(row);
                }}
                color='primary'
              >
                <EditIcon fontSize='small' />
              </IconButton>
            </Tooltip>
            <Tooltip title='View Versions'>
              <IconButton
                size='small'
                onClick={(e) => {
                  e.stopPropagation();
                  handleOpenVersionsDialog(row);
                }}
                color='secondary'
              >
                <HistoryIcon fontSize='small' />
              </IconButton>
            </Tooltip>
            <Tooltip title='Archive Persona'>
              <IconButton
                size='small'
                onClick={(e) => {
                  e.stopPropagation();
                  if (window.confirm('Are you sure you want to Archive this persona?')) {
                    handleDelete(row.id);
                  }
                }}
                color='error'
              >
                <ArchiveIcon fontSize='small' />
              </IconButton>
            </Tooltip>
          </Stack>
        ),
      },
    ];
  };

  const columns = getColumns();

  // Empty state component
  const EmptyState = () => (
    <Box
      sx={{
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        py: 8,
        px: 2,
        textAlign: 'center',
      }}
    >
      <PersonIcon
        sx={{
          fontSize: 64,
          color: alpha(theme.palette.primary.main, 0.2),
          mb: 2,
        }}
      />
      <Typography variant='h6' gutterBottom>
        No Personas Yet
      </Typography>
      <Typography variant='body2' color='text.secondary' sx={{ mb: 3, maxWidth: 450 }}>
        Personas help define the voice and characteristics of your AI assistant. Create your first
        persona to get started.
      </Typography>
      <Button
        variant='contained'
        startIcon={<AddIcon />}
        onClick={handleOpenCreate}
        disabled={actionLoading}
      >
        Add Persona
      </Button>
    </Box>
  );

  // Expose methods to parent component
  useImperativeHandle(ref, () => ({
    handleOpenCreate,
  }));

  return (
    <Box>
      {inProjectDetail && (
        <Box
          sx={{
            display: 'flex',
            justifyContent: 'flex-end',
            alignItems: 'center',
            mb: 3,
          }}
        >
          <Button
            variant='contained'
            startIcon={<AddIcon />}
            onClick={handleOpenCreate}
            disabled={actionLoading}
            sx={{
              backgroundColor: '#28a745',
              color: 'white',
              borderRadius: '8px',
              textTransform: 'none',
              fontWeight: 600,
              px: 3,
              py: 1,
              '&:hover': {
                backgroundColor: '#1e7e34',
              },
            }}
          >
            Add Persona
          </Button>
        </Box>
      )}

      {actionError && (
        <Alert severity='error' sx={{ mb: 3 }}>
          {actionError}
        </Alert>
      )}

      {personas.length === 0 && !loading ? (
        <Paper
          elevation={0}
          sx={{
            border: `1px solid ${alpha(theme.palette.primary.main, 0.1)}`,
            borderRadius: 2,
            overflow: 'hidden',
          }}
        >
          <EmptyState />
        </Paper>
      ) : (
        <Paper
          elevation={0}
          sx={{
            border: `1px solid ${alpha(theme.palette.primary.main, 0.1)}`,
            borderRadius: 2,
            overflow: 'hidden',
          }}
        >
          <DataTable
            data={personas}
            columns={columns}
            loading={loading || actionLoading}
            totalCount={personas.length > 0 ? Math.max(totalCount, personas.length) : totalCount}
            serverSidePagination
            onPageChange={handlePageChange}
            onRowsPerPageChange={(newRowsPerPage) => {
              handleRowsPerPageChange({ target: { value: newRowsPerPage } });
            }}
            emptyStateMessage={loading ? 'Loading...' : 'No personas found'}
            searchEnabled={false}
          />
        </Paper>
      )}

      <PersonaFormModal
        open={modalOpen}
        onClose={() => setModalOpen(false)}
        onSubmit={handleModalSubmit}
        initialValues={modalInitialValues}
        mode={modalMode}
      />

      {/* Versions Dialog */}
      {selectedPersona && (
        <VersionsDialog
          open={versionsDialogOpen}
          onClose={() => setVersionsDialogOpen(false)}
          versions={personaVersions}
          entity={selectedPersona}
          fetchVersions={fetchPersonaVersions}
          createVersion={handleCreatePersonaVersion}
          updateVersion={handleUpdatePersonaVersion}
          fields={[
            {
              name: 'voice_type',
              label: 'Voice Type',
              type: 'select',
              required: true,
              options: [
                { value: 'Puck', label: 'Puck' },
                { value: 'Charon', label: 'Charon' },
                { value: 'Kore', label: 'Kore' },
                { value: 'Custom', label: 'Custom' },
              ],
            },
            {
              name: 'engine',
              label: 'Engine',
              type: 'select',
              required: true,
              options: [
                { value: 'Custom', label: 'Custom' },
                { value: 'Bland', label: 'Bland' },
              ],
            },
            {
              name: 'language',
              label: 'Language',
              type: 'select',
              required: true,
              options: [
                { value: 'English', label: 'English' },
                { value: 'Hindi', label: 'Hindi' },
                { value: 'Kannada', label: 'Kannada' },
                { value: 'Tamil', label: 'Tamil' },
                { value: 'Telugu', label: 'Telugu' },
              ],
            },
            {
              name: 'from_number',
              label: 'Phone Number',
              type: 'select',
              required: true,
              options: [
                { value: '09513886363', label: '09513886363' },
                { value: '02048554672', label: '02048554672' },
              ],
            },
            {
              name: 'status',
              label: 'Status',
              type: 'select',
              required: true,
              options: [
                { value: 'Active', label: 'Active' },
                { value: 'Inactive', label: 'Inactive' },
              ],
            },
            { name: 'change_notes', label: 'Change Notes', type: 'textarea', required: true },
          ]}
          entityType='persona'
        />
      )}
    </Box>
  );
});

export default Personas;
