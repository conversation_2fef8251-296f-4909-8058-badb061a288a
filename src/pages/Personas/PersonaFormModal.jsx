import React from 'react';
import Dialog from '@mui/material/Dialog';
import DialogTitle from '@mui/material/DialogTitle';
import DialogContent from '@mui/material/DialogContent';
import { useTheme } from '@mui/material/styles';
import FormBuilder from '../../components/form/FormBuilder';

const CustomAIVoiceType = {
  PUCK: 'Puck',
  CHARON: 'Charon',
  KORE: 'Kore',
  FENRIR: 'Fenrir',
  AODE: 'Aoede',
  LEDA: 'Leda',
  ORUS: 'Orus',
  ZEPHYR: 'Zephyr',
};

const PersonaEngine = {
  CUSTOM: 'Custom',
  BLAND: 'Bland',
};

const PersonaStatus = {
  DRAFT: 'Draft',
  ACTIVE: 'Active',
  ARCHIVED: 'Archived',
  TESTING: 'Testing',
  INACTIVE: 'Inactive',
};

const getFields = (mode) => {
  const commonFieldProps = {
    gridProps: { xs: 12 },
    size: 'large',
    variant: 'outlined',
    fullWidth: true,
  };

  if (mode === 'edit') {
    return [
      {
        name: 'name',
        type: 'text',
        required: true,
        label: 'Persona Name',
        placeholder: 'Enter persona name *',
        ...commonFieldProps,
      },
      {
        name: 'status',
        type: 'select',
        required: true,
        placeholder: 'Select status *',
        options: Object.entries(PersonaStatus).map(([key, value]) => ({ label: value, value })),
        ...commonFieldProps,
      },
      {
        name: 'is_default',
        type: 'switch',
        required: false,
        placeholder: 'Set as Default',
        ...commonFieldProps,
      },
    ];
  }

  // Create mode fields
  return [
    {
      name: 'name',
      type: 'text',
      required: true,
      label: 'Persona Name',
      placeholder: 'Enter persona name *',
      ...commonFieldProps,
    },
    {
      name: 'voice_type',
      type: 'select',
      required: true,
      placeholder: 'Select voice type *',
      options: Object.entries(CustomAIVoiceType).map(([key, value]) => ({ label: value, value })),
      ...commonFieldProps,
    },
    {
      name: 'engine',
      type: 'select',
      required: true,
      placeholder: 'Select engine *',
      options: Object.entries(PersonaEngine).map(([key, value]) => ({ label: value, value })),
      ...commonFieldProps,
    },
    {
      name: 'language',
      type: 'select',
      required: true,
      placeholder: 'Select language *',
      options: [
        { label: 'English', value: 'English' },
        { label: 'Hindi', value: 'Hindi' },
        { label: 'Tamil', value: 'Tamil' },
        { label: 'Telugu', value: 'Telugu' },
        { label: 'Kannada', value: 'Kannada' },
      ],
      ...commonFieldProps,
    },
    {
      name: 'from_number',
      type: 'select',
      required: true,
      placeholder: 'Select phone number *',
      options: [
        { value: '09513886363', label: '09513886363' },
        { value: '02048554672', label: '02048554672' },
      ],
      ...commonFieldProps,
    },
  ];
};

const PersonaFormModal = ({ open, onClose, onSubmit, initialValues, mode }) => {
  const theme = useTheme();
  const fields = getFields(mode);

  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth='sm'
      fullWidth
      PaperProps={{
        elevation: 1,
        sx: {
          borderRadius: 0,
          overflow: 'hidden',
        },
      }}
    >
      <DialogTitle
        sx={{
          bgcolor: theme.palette.primary.main,
          color: theme.palette.primary.contrastText,
          py: 1.5,
          px: 3,
          fontSize: '1.25rem',
          fontWeight: 600,
          lineHeight: 1.6,
        }}
      >
        {mode === 'edit' ? 'Edit Persona' : 'Add New Persona'}
      </DialogTitle>
      <DialogContent
        sx={{
          p: 3,
          pt: 2,
          '& .MuiOutlinedInput-root': { backgroundColor: '#fff' },
          '& ::placeholder': { color: '#9e9e9e', fontWeight: 400 },
        }}
      >
        <FormBuilder
          fields={fields}
          initialValues={initialValues}
          onSubmit={onSubmit}
          formLayout={{
            fieldSpacing: 0.5,
            direction: 'column',
            variant: 'standard',
            padding: 0,
            buttonAlignment: 'flex-end',
            submitButtonProps: {
              variant: 'contained',
              color: 'primary',
              size: 'large',
              sx: {
                px: 4,
                textTransform: 'none',
                fontWeight: 500,
                fontSize: '0.875rem',
                height: '40px',
                borderRadius: '4px',
                '&:hover': {
                  backgroundColor: theme.palette.primary.dark,
                },
              },
            },
            cancelButtonProps: {
              variant: 'outlined',
              color: 'inherit',
              size: 'large',
              sx: {
                px: 4,
                textTransform: 'none',
                fontWeight: 500,
                fontSize: '0.875rem',
                height: '40px',
                borderRadius: '4px',
                borderColor: 'rgba(0, 0, 0, 0.23)',
                color: 'rgba(0, 0, 0, 0.87)',
                '&:hover': {
                  backgroundColor: 'rgba(0, 0, 0, 0.04)',
                  borderColor: 'rgba(0, 0, 0, 0.23)',
                },
              },
            },
          }}
          submitButtonText={mode === 'edit' ? 'Update' : 'Create'}
          cancelButtonText='Cancel'
          onCancel={onClose}
        />
      </DialogContent>
    </Dialog>
  );
};

export default PersonaFormModal;
