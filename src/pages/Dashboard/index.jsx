import React, { useState, useEffect } from 'react';
import {
  Box,
  Grid,
  Avatar,
  Typography,
  Paper,
  Button,
  CircularProgress,
  Card,
  CardContent,
  MenuItem,
  Select,
  FormControl,
  IconButton,
  Chip,
  Tooltip,
  useTheme,
  Alert,
} from '@mui/material';
import { useProject } from '../../contexts/ProjectContext';
import { AddCall, Refresh, AccessTime, Speed } from '@mui/icons-material';
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip as RechartsTooltip,
  Legend,
  ResponsiveContainer,
  Line,
} from 'recharts';
import PhoneIcon from '@mui/icons-material/Phone';
import DoneIcon from '@mui/icons-material/Done';
import { useNavigate } from 'react-router-dom';
import { format, subDays, formatDistance, parseISO } from 'date-fns';
import {
  getRecentCalls,
  getCallStatistics,
  getCallStatusCounts,
} from '../../services/dashboardService';

/**
 * Dashboard page component
 * Displays project and call statistics, including charts and graphs
 *
 * @returns {JSX.Element} Dashboard component
 */
const Dashboard = () => {
  const theme = useTheme();
  const [loading, setLoading] = useState(true);
  const [stats, setStats] = useState({
    totalCalls: 0,
    completedCalls: 0,
    avgCallDuration: 0,
    successRate: 0,
  });
  const { selectedProjectId } = useProject();
  const [trendData, setTrendData] = useState([]);
  const [recentCalls, setRecentCalls] = useState([]);
  const [refreshing, setRefreshing] = useState(false);
  const [timeFilter, setTimeFilter] = useState('today');
  const [error, setError] = useState(null);
  const navigate = useNavigate();
  const [callsPage, setCallsPage] = useState(0);
  const [callsPerPage, setCallsPerPage] = useState(5);
  const [recentCallsTotal, setRecentCallsTotal] = useState(0);

  useEffect(() => {
    fetchDashboardData();
  }, [timeFilter, selectedProjectId, callsPage, callsPerPage]);

  const getDaysFromFilter = (filter) => {
    switch (filter) {
      case 'today':
        return 1;
      case '7days':
        return 7;
      case '30days':
        return 30;
      case '90days':
        return 90;
      case '365days':
        return 365;
      default:
        return 1;
    }
  };
  const fetchDashboardData = () => {
    setLoading(true);
    // Fetch call statistics for the selected project
    getCallStatistics(selectedProjectId, getDaysFromFilter(timeFilter))
      .then((data) => {
        setStats({
          totalCalls: data.total_calls || 0,
          completedCalls: data.completed_calls || 0,
          avgCallDuration: data.avg_call_duration || 0,
          successRate: data.success_rate || 0,
        });
      })
      .catch((error) => {
        setError(error);
        setStats({
          totalCalls: 0,
          completedCalls: 0,
          avgCallDuration: 0,
          successRate: 0,
        });
      });

    // Fetch  status counts for graph
    getCallStatusCounts(selectedProjectId, getDaysFromFilter(timeFilter))
      .then((data) => {
        if (data && data.dates) {
          const formattedData = Object.entries(data.dates).map(([date, counts]) => {
            const successRate =
              counts.total_calls > 0 ? (counts.completed_calls / counts.total_calls) * 100 : 0;

            const dateObj = new Date(date);
            const dayOfWeek = dateObj.toLocaleDateString('en-US', { weekday: 'short' });
            return {
              date: dayOfWeek,
              totalCalls: counts.total_calls,
              completedCalls: counts.completed_calls,
              failedCalls: counts.failed_calls,
              inProgressCalls: counts.in_progress_calls,
              successRate: successRate,
            };
          });
          setTrendData(formattedData);
        } else {
          setTrendData([]);
        }
      })
      .catch((error) => {
        setError(error);
        setTrendData(generateMockTrendData());
      });

    // Fetch recent calls
    getRecentCalls({
      limit: callsPerPage,
      skip: callsPage * callsPerPage,
      interval: getDaysFromFilter(timeFilter),
      project_id: selectedProjectId,
    })
      .then((data) => {
        setRecentCalls(data.items || []);
        // Store total count if available in the response
        if (data.total) {
          setRecentCallsTotal(data.total);
        }
      })
      .catch((error) => {
        setError(error);
        setRecentCalls(generateMockRecentCalls());
      })
      .finally(() => {
        setLoading(false);
      });
  };

  const generateMockTrendData = () => {
    const days = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'];
    return days.map((date) => {
      const totalCalls = Math.floor(Math.random() * 150) + 50;
      const completedCalls = Math.floor(Math.random() * totalCalls);
      return {
        date,
        totalCalls,
        completedCalls,
        successRate: Math.floor(Math.random() * 30) + 70,
      };
    });
  };

  const generateMockRecentCalls = () => {
    return Array.from({ length: 5 }, (_, index) => ({
      id: index + 1,
      phone_number: `+91 ${Math.floor(Math.random() * 9000000000) + 1000000000}`,
      status: ['completed', 'failed', 'in_progress'][Math.floor(Math.random() * 3)],
      duration: Math.floor(Math.random() * 300) + 60,
      created_at: format(subDays(new Date(), Math.floor(Math.random() * 7)), 'yyyy-MM-dd HH:mm'),
    }));
  };

  const handleStartCall = () => {
    navigate('/call-trigger');
  };

  const handleRefresh = () => {
    setRefreshing(true);
    fetchDashboardData();
    setRefreshing(false);
  };

  const handleViewCall = (callId) => {
    navigate(`/call-logs/${callId}`);
  };

  const handleViewAllCalls = () => {
    navigate('/call-logs');
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'completed':
        return 'success';
      case 'failed':
        return 'error';
      case 'in_progress':
        return 'warning';
      default:
        return 'default';
    }
  };

  const getStatusText = (status) => {
    switch (status) {
      case 'completed':
        return 'Completed';
      case 'failed':
        return 'Failed';
      case 'in_progress':
        return 'In Progress';
      default:
        return 'Unknown';
    }
  };

  /**
   * Format relative time in a human-readable format
   * @param {string} dateTimeString - ISO date string
   * @returns {string} - Formatted relative time (e.g., '5 min ago', '2 hours 30 min ago')
   */
  const formatRelativeTime = (dateTimeString) => {
    if (!dateTimeString) return '';

    try {
      const date =
        typeof dateTimeString === 'string' ? parseISO(dateTimeString) : new Date(dateTimeString);
      return formatDistance(date, new Date(), { addSuffix: true });
    } catch (error) {
      console.error('Error formatting date:', error);
      return '';
    }
  };

  const handleCallsPageChange = (event, newPage) => {
    if (typeof newPage === 'number') {
      setCallsPage(newPage);
    }
  };

  const handleCallsPerPageChange = (event) => {
    if (event && event.target && event.target.value) {
      const newRowsPerPage = parseInt(event.target.value, 10);
      if (!isNaN(newRowsPerPage)) {
        setCallsPerPage(newRowsPerPage);
        setCallsPage(0);
      }
    }
  };

  return (
    <Box sx={{ width: '100%', py: 2, px: 4, bgcolor: '#fafafa', minHeight: '100vh' }}>
      {/* Header Section */}
      <Box sx={{ mb: 1.5, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <Box>
          {error && (
            <Alert severity='error' sx={{ mb: 2, borderRadius: 2 }}>
              Error fetching dashboard data: {error.message}
            </Alert>
          )}
          <Typography variant='body1' color='text.secondary' sx={{ fontSize: '1.1rem' }}>
            Real-time insights into your voice calling performance
          </Typography>
        </Box>
        <Box sx={{ display: 'flex', gap: 2, alignItems: 'center' }}>
          <FormControl
            variant='outlined'
            size='small'
            sx={{
              minWidth: 140,
              '& .MuiOutlinedInput-root': {
                borderRadius: 2,
                bgcolor: 'white',
                boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
                border: 'none',
                '&:hover': {
                  boxShadow: '0 4px 12px rgba(0,0,0,0.15)',
                },
                '&.Mui-focused': {
                  boxShadow: '0 4px 12px rgba(76, 175, 80, 0.3)',
                },
              },
            }}
          >
            <Select
              value={timeFilter}
              onChange={(e) => setTimeFilter(e.target.value)}
              displayEmpty
              sx={{
                '& .MuiSelect-select': {
                  py: 1.5,
                  px: 2,
                  fontWeight: 500,
                  fontSize: '0.875rem',
                },
                '& .MuiPaper-root': {
                  borderRadius: 2,
                  boxShadow: '0 4px 20px rgba(0,0,0,0.1)',
                },
              }}
              MenuProps={{
                PaperProps: {
                  sx: {
                    borderRadius: 2,
                    boxShadow: '0 4px 20px rgba(0,0,0,0.1)',
                    border: '1px solid #e8eaed',
                    mt: 1,
                    '& .MuiMenuItem-root': {
                      fontSize: '0.875rem',
                      fontWeight: 500,
                      py: 1.5,
                      px: 2,
                      color: '#1a1a1a',
                      fontFamily: '"Inter", "Roboto", "Helvetica", "Arial", sans-serif',
                      '&:hover': {
                        backgroundColor: '#f8f9fa',
                        color: '#1a1a1a',
                      },
                      '&.Mui-selected': {
                        backgroundColor: '#e8f0fe',
                        color: '#1565c0',
                        fontWeight: 600,
                        '&:hover': {
                          backgroundColor: '#e3f2fd',
                        },
                      },
                    },
                  },
                },
              }}
            >
              <MenuItem value='today'>Today</MenuItem>
              <MenuItem value='7days'>Last 7 Days</MenuItem>
              <MenuItem value='30days'>Last 30 Days</MenuItem>
              <MenuItem value='90days'>Last 90 Days</MenuItem>
              <MenuItem value='365days'>Last Year</MenuItem>
            </Select>
          </FormControl>
          <Tooltip title='Refresh Data'>
            <IconButton
              onClick={handleRefresh}
              disabled={refreshing}
              sx={{
                bgcolor: 'white',
                color: 'primary.main',
                boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
                borderRadius: 2,
                width: 48,
                height: 48,
                '&:hover': {
                  bgcolor: 'primary.main',
                  color: 'white',
                  boxShadow: '0 4px 12px rgba(76, 175, 80, 0.3)',
                  transform: 'translateY(-2px)',
                },
                '&:disabled': {
                  bgcolor: 'grey.100',
                  color: 'grey.400',
                  transform: 'none',
                },
                transition: 'all 0.3s ease',
              }}
            >
              {refreshing ? <CircularProgress size={20} color='inherit' /> : <Refresh />}
            </IconButton>
          </Tooltip>
          <Button
            variant='contained'
            startIcon={<AddCall />}
            onClick={handleStartCall}
            sx={{
              borderRadius: 2,
              textTransform: 'none',
              px: 3,
              py: 1.5,
              background: 'linear-gradient(135deg, #4CAF50 0%, #66BB6A 100%)',
              color: 'white',
              fontWeight: 600,
              boxShadow: '0 4px 12px rgba(76, 175, 80, 0.3)',
              '&:hover': {
                background: 'linear-gradient(135deg, #388E3C 0%, #4CAF50 100%)',
                boxShadow: '0 6px 16px rgba(76, 175, 80, 0.4)',
                transform: 'translateY(-2px)',
              },
              transition: 'all 0.3s ease',
            }}
          >
            Trigger Call
          </Button>
        </Box>
      </Box>

      {loading ? (
        <Box
          sx={{
            display: 'flex',
            flexDirection: 'column',
            justifyContent: 'center',
            alignItems: 'center',
            my: 8,
            gap: 2,
          }}
        >
          <CircularProgress size={48} thickness={4} />
          <Typography variant='body1' color='text.secondary'>
            Loading dashboard data...
          </Typography>
        </Box>
      ) : (
        <>
          {/* First row: Summary Cards */}
          <Grid container spacing={2.5} sx={{ mb: 2, width: '100%' }}>
            <Grid item xs={12} sm={6} md={3} sx={{ flexGrow: 1 }}>
              <Card
                sx={{
                  height: '100%',
                  borderRadius: 3,
                  bgcolor: 'white',
                  border: '1px solid #e8eaed',
                  boxShadow: '0 2px 8px rgba(0,0,0,0.08)',
                  transition: 'all 0.3s ease',
                  '&:hover': {
                    transform: 'translateY(-2px)',
                    boxShadow: '0 8px 24px rgba(0,0,0,0.12)',
                    borderColor: '#4285f4',
                  },
                }}
              >
                <CardContent sx={{ p: 2, position: 'relative' }}>
                  <Box
                    sx={{
                      display: 'flex',
                      alignItems: 'flex-start',
                      justifyContent: 'space-between',
                    }}
                  >
                    <Box sx={{ flex: 1 }}>
                      <Typography
                        variant='body2'
                        sx={{ color: '#5f6368', mb: 0.5, fontWeight: 500, fontSize: '0.8rem' }}
                      >
                        Total Calls
                      </Typography>
                      <Typography variant='h4' sx={{ fontWeight: 700, mb: 0.25, color: '#202124' }}>
                        {stats.totalCalls}
                      </Typography>
                      <Typography
                        variant='caption'
                        sx={{
                          color: '#5f6368',
                          fontWeight: 400,
                          fontSize: '0.7rem',
                        }}
                      >
                        All triggered calls
                      </Typography>
                    </Box>
                    <Box
                      sx={{
                        bgcolor: '#e8f0fe',
                        borderRadius: 1.5,
                        width: 36,
                        height: 36,
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                      }}
                    >
                      <PhoneIcon sx={{ fontSize: 18, color: '#4285f4' }} />
                    </Box>
                  </Box>
                </CardContent>
              </Card>
            </Grid>
            <Grid item xs={12} sm={6} md={3} lg={3} sx={{ flexGrow: 1 }}>
              <Card
                sx={{
                  height: '100%',
                  borderRadius: 3,
                  bgcolor: 'white',
                  border: '1px solid #e8eaed',
                  boxShadow: '0 2px 8px rgba(0,0,0,0.08)',
                  transition: 'all 0.3s ease',
                  '&:hover': {
                    transform: 'translateY(-2px)',
                    boxShadow: '0 8px 24px rgba(0,0,0,0.12)',
                    borderColor: '#34a853',
                  },
                }}
              >
                <CardContent sx={{ p: 2, position: 'relative' }}>
                  <Box
                    sx={{
                      display: 'flex',
                      alignItems: 'flex-start',
                      justifyContent: 'space-between',
                    }}
                  >
                    <Box sx={{ flex: 1 }}>
                      <Typography
                        variant='body2'
                        sx={{ color: '#5f6368', mb: 0.5, fontWeight: 500, fontSize: '0.8rem' }}
                      >
                        Completed Calls
                      </Typography>
                      <Typography variant='h4' sx={{ fontWeight: 700, mb: 0.25, color: '#202124' }}>
                        {stats.completedCalls}
                      </Typography>
                      <Typography
                        variant='caption'
                        sx={{
                          color: '#5f6368',
                          fontWeight: 400,
                          fontSize: '0.7rem',
                        }}
                      >
                        Successfully finished
                      </Typography>
                    </Box>
                    <Box
                      sx={{
                        bgcolor: '#e6f4ea',
                        borderRadius: 1.5,
                        width: 36,
                        height: 36,
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                      }}
                    >
                      <DoneIcon sx={{ fontSize: 18, color: '#34a853' }} />
                    </Box>
                  </Box>
                </CardContent>
              </Card>
            </Grid>
            <Grid item xs={12} sm={6} md={3} lg={3} sx={{ flexGrow: 1 }}>
              <Card
                sx={{
                  height: '100%',
                  borderRadius: 3,
                  bgcolor: 'white',
                  border: '1px solid #e8eaed',
                  boxShadow: '0 2px 8px rgba(0,0,0,0.08)',
                  transition: 'all 0.3s ease',
                  '&:hover': {
                    transform: 'translateY(-2px)',
                    boxShadow: '0 8px 24px rgba(0,0,0,0.12)',
                    borderColor: '#ff9800',
                  },
                }}
              >
                <CardContent sx={{ p: 2, position: 'relative' }}>
                  <Box
                    sx={{
                      display: 'flex',
                      alignItems: 'flex-start',
                      justifyContent: 'space-between',
                    }}
                  >
                    <Box sx={{ flex: 1 }}>
                      <Typography
                        variant='body2'
                        sx={{ color: '#5f6368', mb: 0.5, fontWeight: 500, fontSize: '0.8rem' }}
                      >
                        Success Rate
                      </Typography>
                      <Typography variant='h4' sx={{ fontWeight: 700, mb: 0.25, color: '#202124' }}>
                        {`${stats.successRate.toFixed(1)}%`}
                      </Typography>
                      <Typography
                        variant='caption'
                        sx={{
                          color: '#5f6368',
                          fontWeight: 400,
                          fontSize: '0.7rem',
                        }}
                      >
                        Call completion rate
                      </Typography>
                    </Box>
                    <Box
                      sx={{
                        bgcolor: '#fff3e0',
                        borderRadius: 1.5,
                        width: 36,
                        height: 36,
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                      }}
                    >
                      <Speed sx={{ fontSize: 18, color: '#ff9800' }} />
                    </Box>
                  </Box>
                </CardContent>
              </Card>
            </Grid>
            <Grid item xs={12} sm={6} md={3} lg={3} sx={{ flexGrow: 1 }}>
              <Card
                sx={{
                  height: '100%',
                  borderRadius: 3,
                  bgcolor: 'white',
                  border: '1px solid #e8eaed',
                  boxShadow: '0 2px 8px rgba(0,0,0,0.08)',
                  transition: 'all 0.3s ease',
                  '&:hover': {
                    transform: 'translateY(-2px)',
                    boxShadow: '0 8px 24px rgba(0,0,0,0.12)',
                    borderColor: '#9c27b0',
                  },
                }}
              >
                <CardContent sx={{ p: 2, position: 'relative' }}>
                  <Box
                    sx={{
                      display: 'flex',
                      alignItems: 'flex-start',
                      justifyContent: 'space-between',
                    }}
                  >
                    <Box sx={{ flex: 1 }}>
                      <Typography
                        variant='body2'
                        sx={{ color: '#5f6368', mb: 0.5, fontWeight: 500, fontSize: '0.8rem' }}
                      >
                        Avg. Call Duration
                      </Typography>
                      <Typography variant='h4' sx={{ fontWeight: 700, mb: 0.25, color: '#202124' }}>
                        {stats.avgCallDuration > 60
                          ? `${Math.floor(stats.avgCallDuration / 60)}m ${Math.round(stats.avgCallDuration % 60)}s`
                          : `${stats.avgCallDuration.toFixed(1)}s`}
                      </Typography>
                      <Typography
                        variant='caption'
                        sx={{
                          color: '#5f6368',
                          fontWeight: 400,
                          fontSize: '0.7rem',
                        }}
                      >
                        Average talk time
                      </Typography>
                    </Box>
                    <Box
                      sx={{
                        bgcolor: '#f3e5f5',
                        borderRadius: 1.5,
                        width: 36,
                        height: 36,
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                      }}
                    >
                      <AccessTime sx={{ fontSize: 18, color: '#9c27b0' }} />
                    </Box>
                  </Box>
                </CardContent>
              </Card>
            </Grid>
          </Grid>

          {/* Second row: Call Trends and Recent Calls */}
          <Grid container spacing={2.5} sx={{ width: '100%', flexWrap: 'nowrap' }}>
            {/* Left side: Call Trends Bar Chart */}
            <Grid item sx={{ flex: '1 1 auto', minWidth: 0 }}>
              <Paper
                sx={{
                  p: 3,
                  height: '100%',
                  bgcolor: 'white',
                  borderRadius: 3,
                  boxShadow: '0 8px 32px rgba(0,0,0,0.1)',
                  border: 'none',
                  display: 'flex',
                  flexDirection: 'column',
                  width: '100%',
                  transition: 'all 0.3s ease',
                  '&:hover': {
                    boxShadow: '0 12px 40px rgba(0,0,0,0.15)',
                  },
                }}
              >
                <Box
                  sx={{
                    display: 'flex',
                    justifyContent: 'space-between',
                    alignItems: 'flex-start',
                    mb: 2,
                  }}
                >
                  <Box sx={{ flex: 1 }}>
                    <Typography variant='h6' sx={{ fontWeight: 600, color: '#1a1a1a', mb: 0.25 }}>
                      Performance Analytics
                    </Typography>
                    <Typography variant='body2' color='text.secondary' sx={{ mb: 1.5 }}>
                      Call volume and success rate trends over the last 7 days
                    </Typography>

                    {/* Key Metrics Summary */}
                    <Box sx={{ display: 'flex', gap: 3, mb: 1 }}>
                      <Box>
                        <Typography variant='caption' sx={{ color: '#5f6368', fontSize: '0.7rem' }}>
                          Total Volume
                        </Typography>
                        <Typography variant='body2' sx={{ fontWeight: 600, color: '#4285f4' }}>
                          {trendData.reduce((sum, day) => sum + day.totalCalls, 0)} calls
                        </Typography>
                      </Box>
                      <Box>
                        <Typography variant='caption' sx={{ color: '#5f6368', fontSize: '0.7rem' }}>
                          Avg Success Rate
                        </Typography>
                        <Typography variant='body2' sx={{ fontWeight: 600, color: '#34a853' }}>
                          {(
                            trendData.reduce((sum, day) => sum + day.successRate, 0) /
                            trendData.length
                          ).toFixed(1)}
                          %
                        </Typography>
                      </Box>
                      <Box>
                        <Typography variant='caption' sx={{ color: '#5f6368', fontSize: '0.7rem' }}>
                          Peak Day
                        </Typography>
                        <Typography variant='body2' sx={{ fontWeight: 600, color: '#ff9800' }}>
                          {trendData.reduce(
                            (max, day) => (day.totalCalls > max.totalCalls ? day : max),
                            trendData[0]
                          )?.date || 'N/A'}
                        </Typography>
                      </Box>
                    </Box>
                  </Box>

                  <Box
                    sx={{
                      bgcolor: '#e8f5e9',
                      borderRadius: 2,
                      px: 2,
                      py: 1,
                      display: 'flex',
                      alignItems: 'center',
                      gap: 1,
                      border: '1px solid #c8e6c9',
                    }}
                  >
                    <Box sx={{ width: 6, height: 6, borderRadius: '50%', bgcolor: '#4caf50' }} />
                    <Typography
                      variant='caption'
                      sx={{ fontWeight: 500, color: '#2e7d32', fontSize: '0.75rem' }}
                    >
                      Live Data
                    </Typography>
                  </Box>
                </Box>
                <Box
                  sx={{
                    flexGrow: 1,
                    minHeight: 280,
                    width: '100%',
                    overflow: 'hidden',
                    position: 'relative',
                  }}
                >
                  {/* Chart Controls */}
                  <Box
                    sx={{
                      position: 'absolute',
                      top: 10,
                      right: 10,
                      zIndex: 10,
                      display: 'flex',
                      gap: 1,
                    }}
                  >
                    <Box
                      sx={{
                        bgcolor: 'rgba(255,255,255,0.9)',
                        borderRadius: 1,
                        px: 1.5,
                        py: 0.5,
                        border: '1px solid #e8eaed',
                        backdropFilter: 'blur(4px)',
                      }}
                    >
                      <Typography variant='caption' sx={{ fontSize: '0.7rem', color: '#5f6368' }}>
                        Last 7 Days
                      </Typography>
                    </Box>
                  </Box>

                  <ResponsiveContainer width='100%' height={280}>
                    <BarChart
                      data={trendData}
                      margin={{
                        top: 30,
                        right: 25,
                        left: 15,
                        bottom: 30,
                      }}
                      barCategoryGap='25%'
                      barGap={4}
                    >
                      {/* Enhanced Grid */}
                      <CartesianGrid
                        strokeDasharray='2 4'
                        stroke='#f0f2f5'
                        vertical={false}
                        horizontalPoints={[0, 25, 50, 75, 100]}
                      />

                      {/* X-Axis with better formatting */}
                      <XAxis
                        dataKey='date'
                        tick={{ fill: '#5f6368', fontSize: 11, fontWeight: 500 }}
                        axisLine={{ stroke: '#e8eaed', strokeWidth: 1 }}
                        tickLine={false}
                        tickMargin={8}
                        interval={0}
                      />

                      {/* Left Y-Axis for call counts */}
                      <YAxis
                        yAxisId='left'
                        orientation='left'
                        tick={{ fill: '#5f6368', fontSize: 11 }}
                        axisLine={false}
                        tickLine={false}
                        tickMargin={8}
                        label={{
                          value: 'Number of Calls',
                          angle: -90,
                          position: 'insideLeft',
                          style: { textAnchor: 'middle', fill: '#5f6368', fontSize: '12px' },
                        }}
                      />

                      {/* Right Y-Axis for success rate */}
                      <YAxis
                        yAxisId='right'
                        orientation='right'
                        tick={{ fill: '#5f6368', fontSize: 11 }}
                        axisLine={false}
                        tickLine={false}
                        tickMargin={8}
                        domain={[0, 100]}
                        tickFormatter={(value) => `${value}%`}
                        label={{
                          value: 'Success Rate (%)',
                          angle: 90,
                          position: 'insideRight',
                          style: { textAnchor: 'middle', fill: '#5f6368', fontSize: '12px' },
                        }}
                      />

                      {/* Enhanced Tooltip */}
                      <RechartsTooltip
                        contentStyle={{
                          backgroundColor: 'rgba(255,255,255,0.98)',
                          border: 'none',
                          borderRadius: 12,
                          boxShadow: '0 8px 32px rgba(0,0,0,0.12)',
                          padding: '12px 16px',
                          backdropFilter: 'blur(8px)',
                        }}
                        labelStyle={{
                          color: '#1a1a1a',
                          fontWeight: 600,
                          marginBottom: '8px',
                          fontSize: '13px',
                        }}
                        formatter={(value, name, props) => {
                          const colors = {
                            'Total Calls': '#4285f4',
                            'Completed Calls': '#34a853',
                            'Success Rate': '#ff9800',
                          };

                          if (name === 'Success Rate') {
                            return [
                              <span style={{ color: colors[name], fontWeight: 600 }}>
                                {value}%
                              </span>,
                              name,
                            ];
                          }
                          return [
                            <span style={{ color: colors[name], fontWeight: 600 }}>{value}</span>,
                            name,
                          ];
                        }}
                        labelFormatter={(label) => `Date: ${label}`}
                        cursor={{ fill: 'rgba(66, 133, 244, 0.05)' }}
                      />

                      {/* Professional Legend */}
                      <Legend
                        verticalAlign='top'
                        height={35}
                        iconType='rect'
                        wrapperStyle={{
                          fontSize: '12px',
                          color: '#5f6368',
                          paddingBottom: '10px',
                        }}
                        formatter={(value) => <span style={{ fontWeight: 500 }}>{value}</span>}
                      />

                      {/* Enhanced Bars */}
                      <Bar
                        yAxisId='left'
                        dataKey='totalCalls'
                        name='Total Calls'
                        fill='url(#totalCallsGradient)'
                        radius={[3, 3, 0, 0]}
                        barSize={20}
                      />
                      <Bar
                        yAxisId='left'
                        dataKey='completedCalls'
                        name='Completed Calls'
                        fill='url(#completedCallsGradient)'
                        radius={[3, 3, 0, 0]}
                        barSize={20}
                      />

                      {/* Enhanced Line */}
                      <Line
                        yAxisId='right'
                        type='monotone'
                        dataKey='successRate'
                        name='Success Rate'
                        stroke='#ff9800'
                        strokeWidth={3}
                        dot={{
                          r: 5,
                          fill: '#ff9800',
                          strokeWidth: 3,
                          stroke: 'white',
                          filter: 'drop-shadow(0 2px 4px rgba(255,152,0,0.3))',
                        }}
                        activeDot={{
                          r: 7,
                          fill: '#ff9800',
                          strokeWidth: 3,
                          stroke: 'white',
                          filter: 'drop-shadow(0 4px 8px rgba(255,152,0,0.4))',
                        }}
                        connectNulls={false}
                      />

                      {/* Gradient Definitions */}
                      <defs>
                        <linearGradient id='totalCallsGradient' x1='0' y1='0' x2='0' y2='1'>
                          <stop offset='0%' stopColor='#4285f4' stopOpacity={0.8} />
                          <stop offset='100%' stopColor='#4285f4' stopOpacity={0.3} />
                        </linearGradient>
                        <linearGradient id='completedCallsGradient' x1='0' y1='0' x2='0' y2='1'>
                          <stop offset='0%' stopColor='#34a853' stopOpacity={0.8} />
                          <stop offset='100%' stopColor='#34a853' stopOpacity={0.3} />
                        </linearGradient>
                      </defs>
                    </BarChart>
                  </ResponsiveContainer>
                </Box>
              </Paper>
            </Grid>

            {/* Right side: Recent Calls List */}
            <Grid item sx={{ width: '320px', flexShrink: 0 }}>
              <Paper
                sx={{
                  p: 3,
                  height: '100%',
                  display: 'flex',
                  flexDirection: 'column',
                  bgcolor: 'white',
                  borderRadius: 3,
                  boxShadow: '0 2px 8px rgba(0,0,0,0.08)',
                  border: '1px solid #e8eaed',
                  width: '100%',
                  transition: 'all 0.3s ease',
                  '&:hover': {
                    boxShadow: '0 8px 24px rgba(0,0,0,0.12)',
                  },
                }}
              >
                <Box
                  sx={{
                    display: 'flex',
                    justifyContent: 'space-between',
                    alignItems: 'center',
                    mb: 1.5,
                  }}
                >
                  <Box>
                    <Typography variant='h6' sx={{ fontWeight: 600, color: '#1a1a1a', mb: 0.1 }}>
                      Recent Activity
                    </Typography>
                    <Typography
                      variant='caption'
                      color='text.secondary'
                      sx={{ fontSize: '0.75rem' }}
                    >
                      Latest call attempts
                    </Typography>
                  </Box>
                  <Button
                    variant='outlined'
                    size='small'
                    onClick={handleViewAllCalls}
                    sx={{
                      textTransform: 'none',
                      borderRadius: 2,
                      fontWeight: 500,
                      borderColor: '#e0e0e0',
                      color: 'text.primary',
                      '&:hover': {
                        borderColor: 'primary.main',
                        bgcolor: 'primary.main',
                        color: 'white',
                      },
                    }}
                  >
                    View All
                  </Button>
                </Box>
                <Box sx={{ flexGrow: 1, overflow: 'auto', maxHeight: '400px' }}>
                  {recentCalls.length > 0 ? (
                    <Box sx={{ display: 'flex', flexDirection: 'column' }}>
                      {recentCalls.map((call, index) => (
                        <Box
                          key={call.id}
                          sx={{
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'space-between',
                            py: 1.5,
                            px: 1,
                            borderBottom: '1px solid #f0f0f0',
                            cursor: 'pointer',
                            transition: 'all 0.2s ease',
                            '&:hover': {
                              bgcolor: '#f8f9fa',
                              borderRadius: 1,
                            },
                            '&:last-child': {
                              borderBottom: 'none',
                            },
                          }}
                          onClick={() => handleViewCall(call.id)}
                        >
                          {/* Left section - Phone icon and number */}
                          <Box
                            sx={{
                              display: 'flex',
                              alignItems: 'center',
                              gap: 1.5,
                              flex: 1,
                              minWidth: 0,
                            }}
                          >
                            <Box
                              sx={{
                                width: 24,
                                height: 24,
                                borderRadius: '50%',
                                bgcolor: '#e8f0fe',
                                display: 'flex',
                                alignItems: 'center',
                                justifyContent: 'center',
                                flexShrink: 0,
                              }}
                            >
                              <PhoneIcon sx={{ fontSize: 12, color: '#4285f4' }} />
                            </Box>
                            <Box sx={{ flex: 1, minWidth: 0 }}>
                              <Typography
                                variant='body2'
                                sx={{
                                  fontWeight: 500,
                                  fontSize: '0.85rem',
                                  color: '#1a1a1a',
                                  overflow: 'hidden',
                                  textOverflow: 'ellipsis',
                                  whiteSpace: 'nowrap',
                                }}
                              >
                                {call.to_number}
                              </Typography>
                              <Typography
                                variant='caption'
                                sx={{
                                  color: 'text.secondary',
                                  fontSize: '0.7rem',
                                  display: 'block',
                                }}
                              >
                                {call.created_at ? formatRelativeTime(call.created_at) : ''}
                              </Typography>
                            </Box>
                          </Box>

                          {/* Right section - Status */}
                          <Box sx={{ flexShrink: 0, ml: 1 }}>
                            <Chip
                              size='small'
                              label={getStatusText(call.status)}
                              color={getStatusColor(call.status)}
                              sx={{
                                borderRadius: 1,
                                fontWeight: 500,
                                height: '20px',
                                fontSize: '0.65rem',
                                textTransform: 'capitalize',
                                '& .MuiChip-label': {
                                  px: 1,
                                },
                              }}
                            />
                          </Box>
                        </Box>
                      ))}
                    </Box>
                  ) : (
                    <Box sx={{ py: 4, textAlign: 'center' }}>
                      <Box
                        sx={{
                          width: 48,
                          height: 48,
                          borderRadius: '50%',
                          bgcolor: '#f0f0f0',
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center',
                          mx: 'auto',
                          mb: 1.5,
                        }}
                      >
                        <PhoneIcon sx={{ fontSize: 20, color: '#999' }} />
                      </Box>
                      <Typography variant='body2' sx={{ fontWeight: 500, mb: 0.5 }}>
                        No recent calls
                      </Typography>
                      <Typography
                        variant='caption'
                        color='text.secondary'
                        sx={{ fontSize: '0.75rem' }}
                      >
                        Your call activity will appear here
                      </Typography>
                    </Box>
                  )}
                </Box>
              </Paper>
            </Grid>
          </Grid>
        </>
      )}
    </Box>
  );
};

export default Dashboard;
