import React from 'react';
import { TextField as MuiTextField } from '@mui/material';
import { useField } from 'formik';

/**
 * TextField component for forms
 * @param {Object} props - Component properties
 * @returns {JSX.Element}
 */
const TextField = ({ label, helperText, required, placeholder, ...props }) => {
  const [field, meta] = useField(props);
  const isError = Boolean(meta.touched && meta.error);
  const displayLabel = required ? `${label} *` : label;
  return (
    <MuiTextField
      {...field}
      {...props}
      label={displayLabel}
      error={isError}
      helperText={null}
      fullWidth
      variant='outlined'
      margin='normal'
    />
  );
};

export default TextField;
