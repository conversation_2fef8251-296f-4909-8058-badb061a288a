import React from 'react';
import {
  AppBar,
  Toolbar,
  Typography,
  IconButton,
  Box,
  Avatar,
  Menu,
  MenuItem,
  Tooltip,
  Divider,
} from '@mui/material';
import MenuIcon from '@mui/icons-material/Menu';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import { useAuth } from '../../contexts/AuthContext';
import { useProject } from '../../contexts/ProjectContext';
import { useState } from 'react';
import { useLocation } from 'react-router-dom';

/**
 * Application header component with hamburger menu and user profile
 * @param {Object} props - Component props
 * @param {boolean} props.open - Whether sidebar is open
 * @param {Function} props.handleDrawerToggle - Function to toggle sidebar
 * @returns {JSX.Element} AppHeader component
 */
const AppHeader = ({ open, handleDrawerToggle }) => {
  const { user, logout } = useAuth();
  const { projects, selectedProjectId, setSelectedProjectId, refreshProjects } = useProject();
  const [anchorEl, setAnchorEl] = useState(null);
  const [projectMenuAnchor, setProjectMenuAnchor] = useState(null);
  const location = useLocation();

  // Function to get current page title based on route
  const getCurrentPageTitle = () => {
    const path = location.pathname;
    const pageMap = {
      '/dashboard': 'Dashboard',
      '/projects': 'Projects',
      '/call-trigger': 'Call Trigger',
      '/call-logs': 'Call Logs',
      '/analytics': 'Analytics',
      '/configuration': 'Configuration',
    };
    return pageMap[path] || 'Dashboard';
  };

  const handleMenu = (event) => {
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  const handleLogout = () => {
    handleClose();
    logout();
  };

  // Handle project selection
  const handleProjectSelect = (projectId) => {
    setSelectedProjectId(projectId);
    // Refresh data when project changes
    refreshProjects();
    handleClose();
  };

  return (
    <AppBar
      position='fixed'
      sx={{
        zIndex: (theme) => theme.zIndex.drawer + 1,
        boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
        backgroundColor: 'white',
        borderBottom: '1px solid rgba(0,0,0,0.08)',
        backdropFilter: 'blur(8px)',
      }}
    >
      <Toolbar
        sx={{
          minHeight: '56px !important',
          px: { xs: 2, sm: 3 },
          gap: 2,
        }}
      >
        {/* Left section - Hamburger and App name aligned with sidebar */}
        <Box
          sx={{
            display: 'flex',
            alignItems: 'center',
            width: '200px', // Match sidebar width exactly
            pl: 0, // Remove container padding to move closer to edge
          }}
        >
          <IconButton
            color='inherit'
            aria-label='toggle drawer'
            edge='start'
            onClick={handleDrawerToggle}
            sx={{
              color: 'primary.main',
              '&:hover': {
                backgroundColor: 'rgba(76, 175, 80, 0.08)',
                transform: 'scale(1.05)',
              },
              '&:active': {
                transform: 'rotate(90deg) scale(1.05)',
              },
              transition: 'all 0.3s ease-in-out',
              borderRadius: '10px',
              p: 0.75,
              mr: 1.25,
              ml: 1, // Position to align with sidebar icons leftmost edge
            }}
          >
            <MenuIcon
              sx={{
                transition: 'transform 0.3s ease-in-out',
                transform: 'rotate(0deg)', // Always horizontal - remove state-based rotation
              }}
            />
          </IconButton>

          <Typography
            variant='h6'
            component='div'
            sx={{
              fontSize: '1.1rem',
              fontWeight: 600,
              color: 'primary.main',
              letterSpacing: '0.2px',
              flexGrow: 1,
            }}
          >
            Vegrow Voice
          </Typography>
        </Box>

        {/* Center section - Current page title */}
        <Box
          sx={{
            flexGrow: 1,
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            px: 3,
          }}
        >
          <Typography
            variant='h5'
            component='div'
            sx={{
              fontSize: '1.25rem',
              fontWeight: 600,
              color: '#1a1a1a',
              textAlign: 'center',
              letterSpacing: '0.2px',
            }}
          >
            {getCurrentPageTitle()}
          </Typography>
        </Box>

        {/* Right section - User info and project */}
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
          {user && (
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1.5 }}>
              {/* User info section */}
              <Box sx={{ textAlign: 'right', minWidth: 0, width: '150px' }}>
                <Typography
                  variant='body1'
                  sx={{
                    fontWeight: 600,
                    color: '#1a1a1a',
                    fontSize: '0.9rem',
                    lineHeight: 1.1,
                    whiteSpace: 'nowrap',
                    overflow: 'hidden',
                    textOverflow: 'ellipsis',
                    width: '100%',
                    textAlign: 'right',
                  }}
                >
                  {user.name}
                </Typography>
                <Box
                  onClick={(e) => setProjectMenuAnchor(e.currentTarget)}
                  sx={{
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'flex-end',
                    gap: 0.5,
                    cursor: 'pointer',
                    mt: 0.5,
                    width: '100%',
                    '&:hover': {
                      '& .project-text': {
                        color: '#4caf50',
                      },
                      '& .dropdown-icon': {
                        color: '#4caf50',
                      },
                    },
                  }}
                >
                  <Typography
                    variant='caption'
                    className='project-text'
                    sx={{
                      color: '#5f6368',
                      fontSize: '0.75rem',
                      fontWeight: 500,
                      transition: 'color 0.2s ease',
                      whiteSpace: 'nowrap',
                      overflow: 'hidden',
                      textOverflow: 'ellipsis',
                      flex: 1,
                      textAlign: 'right',
                    }}
                  >
                    {projects.find((project) => project.id === selectedProjectId)?.title ||
                      'Select Project'}
                  </Typography>
                  <ExpandMoreIcon
                    className='dropdown-icon'
                    sx={{
                      fontSize: 14,
                      color: '#5f6368',
                      transition: 'color 0.2s ease',
                      flexShrink: 0,
                    }}
                  />
                </Box>
              </Box>

              {/* Project selection menu */}
              <Menu
                anchorEl={projectMenuAnchor}
                open={Boolean(projectMenuAnchor)}
                onClose={() => setProjectMenuAnchor(null)}
                sx={{
                  '& .MuiPaper-root': {
                    borderRadius: 2,
                    boxShadow: '0 4px 20px rgba(0,0,0,0.1)',
                    border: '1px solid #e8eaed',
                    mt: 1,
                    minWidth: '200px',
                  },
                }}
              >
                <MenuItem disabled>
                  <Typography variant='body2' color='textSecondary'>
                    Switch Project
                  </Typography>
                </MenuItem>
                <Divider />
                {projects.map((project) => (
                  <MenuItem
                    key={project.id}
                    onClick={() => {
                      handleProjectSelect(project.id);
                      setProjectMenuAnchor(null);
                    }}
                    sx={{
                      backgroundColor:
                        selectedProjectId === project.id
                          ? 'rgba(76, 175, 80, 0.08)'
                          : 'transparent',
                      '&:hover': {
                        backgroundColor:
                          selectedProjectId === project.id
                            ? 'rgba(76, 175, 80, 0.12)'
                            : 'rgba(0, 0, 0, 0.04)',
                      },
                    }}
                  >
                    {project.title}
                    {selectedProjectId === project.id && (
                      <Box component='span' sx={{ ml: 1, color: 'primary.main' }}>
                        ✓
                      </Box>
                    )}
                  </MenuItem>
                ))}
              </Menu>

              {/* User avatar with menu */}
              <Box>
                <Tooltip title='Account settings'>
                  <IconButton onClick={handleMenu} sx={{ p: 0 }}>
                    <Avatar
                      alt={user.name || 'User'}
                      src={user.picture}
                      sx={{ width: 40, height: 40 }}
                    />
                  </IconButton>
                </Tooltip>

                <Menu
                  id='menu-appbar'
                  anchorEl={anchorEl}
                  keepMounted
                  open={Boolean(anchorEl)}
                  onClose={handleClose}
                  anchorOrigin={{
                    vertical: 'bottom',
                    horizontal: 'right',
                  }}
                  transformOrigin={{
                    vertical: 'top',
                    horizontal: 'right',
                  }}
                  sx={{
                    '& .MuiPaper-root': {
                      borderRadius: 2,
                      boxShadow: '0 4px 20px rgba(0,0,0,0.1)',
                      border: '1px solid #e8eaed',
                      mt: 1,
                    },
                  }}
                >
                  <MenuItem>
                    <Typography>{user.name}</Typography>
                  </MenuItem>
                  <MenuItem>
                    <Typography variant='body2' color='textSecondary'>
                      {user.email}
                    </Typography>
                  </MenuItem>
                  <Divider />
                  <MenuItem onClick={handleLogout}>Logout</MenuItem>
                </Menu>
              </Box>
            </Box>
          )}
        </Box>
      </Toolbar>
    </AppBar>
  );
};

export default AppHeader;
