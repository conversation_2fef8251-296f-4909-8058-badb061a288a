import { useSnackbar } from 'notistack';

const useNotify = () => {
  const { enqueueSnackbar } = useSnackbar();

  const getNotified = (message, variant) => {
    enqueueSnackbar(message, {
      variant,
      anchorOrigin: {
        vertical: 'top',
        horizontal: 'right',
      },
    });
  };

  return (message, variant = 'success') => getNotified(message, variant);
};

export default useNotify;
